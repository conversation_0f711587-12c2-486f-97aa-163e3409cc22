const express = require('express');
const router = express.Router();
const db = require('../config/database');

// GET /api/episodes/content/:contentId/seasons - Get all seasons for content
router.get('/content/:contentId/seasons', async (req, res) => {
  try {
    const { contentId } = req.params;
    
    // Get all seasons for this content
    const seasonsResult = await db.execute(
      'SELECT * FROM seasons WHERE content_id = ? ORDER BY season_number ASC',
      [contentId]
    );
    
    const seasons = Array.isArray(seasonsResult) ? seasonsResult[0] || [] : seasonsResult || [];
    
    // Get episodes for each season
    for (const season of seasons) {
      const episodesResult = await db.execute(
        'SELECT * FROM episodes WHERE season_id = ? ORDER BY episode_number ASC',
        [season.id]
      );
      season.episodes = Array.isArray(episodesResult) ? episodesResult[0] || [] : episodesResult || [];
    }
    
    res.json({ seasons });
  } catch (error) {
    console.error('Error fetching seasons:', error);
    res.status(500).json({ error: 'Failed to fetch seasons' });
  }
});

// POST /api/episodes/content/:contentId/seasons - Create new season
router.post('/content/:contentId/seasons', async (req, res) => {
  try {
    const { contentId } = req.params;
    const { season_number, title, description } = req.body;
    
    if (!season_number) {
      return res.status(400).json({ error: 'Season number is required' });
    }
    
    // Check if season already exists
    const existingResult = await db.execute(
      'SELECT * FROM seasons WHERE content_id = ? AND season_number = ?',
      [contentId, season_number]
    );
    
    const existingSeason = Array.isArray(existingResult) ? existingResult[0] || [] : existingResult || [];
    if (existingSeason.length > 0) {
      return res.status(400).json({ error: 'Season already exists' });
    }
    
    // Generate unique season ID
    const seasonId = `season_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Create new season
    await db.execute(
      'INSERT INTO seasons (id, content_id, season_number, title, description, created_at) VALUES (?, ?, ?, ?, ?, NOW())',
      [seasonId, contentId, season_number, title || `Season ${season_number}`, description || '']
    );
    
    // Get the created season
    const seasonResult = await db.execute(
      'SELECT * FROM seasons WHERE id = ?',
      [seasonId]
    );
    
    const season = Array.isArray(seasonResult) ? seasonResult[0]?.[0] : seasonResult;
    if (!season) {
      throw new Error('Failed to retrieve created season');
    }
    
    res.status(201).json({ season });
  } catch (error) {
    console.error('Database error creating season:', error);
    res.status(500).json({ error: 'Failed to create season in database' });
  }
});

// POST /api/episodes/content/:contentId/seasons/:seasonId/episodes - Create new episode
router.post('/content/:contentId/seasons/:seasonId/episodes', async (req, res) => {
  try {
    const { seasonId, contentId } = req.params;
    const { episode_number, title, description, secure_video_links, runtime } = req.body;
    
    if (!episode_number) {
      return res.status(400).json({ error: 'Episode number is required' });
    }
    
    // Check if episode already exists
    const existingResult = await db.execute(
      'SELECT * FROM episodes WHERE season_id = ? AND episode_number = ?',
      [seasonId, episode_number]
    );
    
    const existingEpisode = Array.isArray(existingResult) ? existingResult[0] || [] : existingResult || [];
    if (existingEpisode.length > 0) {
      return res.status(400).json({ error: 'Episode already exists' });
    }
    
    // Generate unique episode ID
    const episodeId = `episode_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Create new episode
    await db.execute(
      'INSERT INTO episodes (id, season_id, content_id, episode_number, title, description, secure_video_links, runtime, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())',
      [episodeId, seasonId, contentId, episode_number, title || `Episode ${episode_number}`, description || '', secure_video_links || '', runtime || '']
    );
    
    // Get the created episode
    const episodeResult = await db.execute(
      'SELECT * FROM episodes WHERE id = ?',
      [episodeId]
    );
    
    const episode = Array.isArray(episodeResult) ? episodeResult[0]?.[0] : episodeResult;
    if (!episode) {
      throw new Error('Failed to retrieve created episode');
    }
    
    res.status(201).json({ episode });
  } catch (error) {
    console.error('Database error creating episode:', error);
    res.status(500).json({ error: 'Failed to create episode in database' });
  }
});

// PUT /api/episodes/content/:contentId/seasons/:seasonId - Update season
router.put('/content/:contentId/seasons/:seasonId', async (req, res) => {
  try {
    const { seasonId } = req.params;
    const { title, description } = req.body;
    
    const result = await db.execute(
      'UPDATE seasons SET title = ?, description = ?, updated_at = NOW() WHERE id = ?',
      [title, description, seasonId]
    );
    
    const affectedRows = Array.isArray(result) ? result[0]?.affectedRows : result?.affectedRows;
    if (!affectedRows || affectedRows === 0) {
      return res.status(404).json({ error: 'Season not found' });
    }
    
    // Get updated season
    const seasonResult = await db.execute(
      'SELECT * FROM seasons WHERE id = ?',
      [seasonId]
    );
    
    const season = Array.isArray(seasonResult) ? seasonResult[0]?.[0] : seasonResult;
    res.json({ season });
  } catch (error) {
    console.error('Database error updating season:', error);
    res.status(500).json({ error: 'Failed to update season' });
  }
});

// PUT /api/episodes/content/:contentId/seasons/:seasonId/episodes/:episodeId - Update episode
router.put('/content/:contentId/seasons/:seasonId/episodes/:episodeId', async (req, res) => {
  try {
    const { episodeId } = req.params;
    const { title, description, secure_video_links, runtime } = req.body;
    
    const result = await db.execute(
      'UPDATE episodes SET title = ?, description = ?, secure_video_links = ?, runtime = ?, updated_at = NOW() WHERE id = ?',
      [title, description, secure_video_links, runtime, episodeId]
    );
    
    const affectedRows = Array.isArray(result) ? result[0]?.affectedRows : result?.affectedRows;
    if (!affectedRows || affectedRows === 0) {
      return res.status(404).json({ error: 'Episode not found' });
    }
    
    // Get updated episode
    const episodeResult = await db.execute(
      'SELECT * FROM episodes WHERE id = ?',
      [episodeId]
    );
    
    const episode = Array.isArray(episodeResult) ? episodeResult[0]?.[0] : episodeResult;
    res.json({ episode });
  } catch (error) {
    console.error('Database error updating episode:', error);
    res.status(500).json({ error: 'Failed to update episode' });
  }
});

// DELETE /api/episodes/content/:contentId/seasons/:seasonId - Delete season
router.delete('/content/:contentId/seasons/:seasonId', async (req, res) => {
  try {
    const { seasonId } = req.params;
    
    // Delete all episodes in this season first
    await db.execute('DELETE FROM episodes WHERE season_id = ?', [seasonId]);
    
    // Delete the season
    const result = await db.execute('DELETE FROM seasons WHERE id = ?', [seasonId]);
    
    const affectedRows = Array.isArray(result) ? result[0]?.affectedRows : result?.affectedRows;
    if (!affectedRows || affectedRows === 0) {
      return res.status(404).json({ error: 'Season not found' });
    }
    
    res.json({ message: 'Season deleted successfully' });
  } catch (error) {
    console.error('Database error deleting season:', error);
    res.status(500).json({ error: 'Failed to delete season' });
  }
});

// DELETE /api/episodes/content/:contentId/seasons/:seasonId/episodes/:episodeId - Delete episode
router.delete('/content/:contentId/seasons/:seasonId/episodes/:episodeId', async (req, res) => {
  try {
    const { episodeId } = req.params;
    
    const result = await db.execute('DELETE FROM episodes WHERE id = ?', [episodeId]);
    
    const affectedRows = Array.isArray(result) ? result[0]?.affectedRows : result?.affectedRows;
    if (!affectedRows || affectedRows === 0) {
      return res.status(404).json({ error: 'Episode not found' });
    }
    
    res.json({ message: 'Episode deleted successfully' });
  } catch (error) {
    console.error('Database error deleting episode:', error);
    res.status(500).json({ error: 'Failed to delete episode' });
  }
});

module.exports = router;
