/**
 * OMDb API Service
 *
 * Service for interacting with the Open Movie Database API
 * Handles movies and TV series search functionality
 */

// OMDb API Configuration
const OMDB_API_KEY = import.meta.env.VITE_OMDB_API_KEY || '';
const OMDB_BASE_URL = import.meta.env.VITE_OMDB_BASE_URL || 'http://www.omdbapi.com';

// Rate limiting configuration
const RATE_LIMIT_DELAY = 200; // 5 requests per second with safety margin
let lastRequestTime = 0;

// OMDb API Response Interfaces
export interface OMDbMovie {
  imdbID: string;
  Title: string;
  Year: string;
  Type: 'movie' | 'series' | 'episode';
  Poster: string;
  Plot?: string;
  Director?: string;
  Writer?: string;
  Actors?: string;
  Genre?: string;
  Runtime?: string;
  imdbRating?: string;
  Released?: string;
  Country?: string;
  Language?: string;
  Awards?: string;
  totalSeasons?: string;
}

export interface OMDbSearchResponse {
  Search: OMDbMovie[];
  totalResults: string;
  Response: string;
  Error?: string;
}

export interface OMDbDetailResponse extends OMDbMovie {
  Response: string;
  Error?: string;
}

/**
 * Rate limiting helper
 */
async function rateLimit(): Promise<void> {
  const now = Date.now();
  const timeSinceLastRequest = now - lastRequestTime;
  
  if (timeSinceLastRequest < RATE_LIMIT_DELAY) {
    await new Promise(resolve => setTimeout(resolve, RATE_LIMIT_DELAY - timeSinceLastRequest));
  }
  
  lastRequestTime = Date.now();
}

/**
 * Make API request to OMDb
 */
async function makeOMDbRequest(params: Record<string, string>): Promise<any> {
  await rateLimit();

  if (!OMDB_API_KEY) {
    throw new Error('OMDb API key is not configured');
  }

  const url = new URL(OMDB_BASE_URL);
  url.searchParams.append('apikey', OMDB_API_KEY);
  
  Object.entries(params).forEach(([key, value]) => {
    if (value) {
      url.searchParams.append(key, value);
    }
  });

  try {
    const response = await fetch(url.toString());
    
    if (!response.ok) {
      throw new Error(`OMDb API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.Response === 'False') {
      throw new Error(data.Error || 'OMDb API request failed');
    }

    return data;
  } catch (error) {
    console.error('OMDb API Error:', error);
    throw error;
  }
}

/**
 * Search for movies and TV series
 */
export async function searchOMDbContent(
  query: string,
  type?: 'movie' | 'series',
  year?: string,
  page: number = 1
): Promise<OMDbSearchResponse> {
  const params: Record<string, string> = {
    s: query,
    page: page.toString()
  };

  if (type) {
    params.type = type;
  }

  if (year) {
    params.y = year;
  }

  return await makeOMDbRequest(params);
}

/**
 * Get detailed information by IMDb ID
 */
export async function getOMDbDetails(imdbId: string, plot: 'short' | 'full' = 'short'): Promise<OMDbDetailResponse> {
  const params: Record<string, string> = {
    i: imdbId,
    plot: plot
  };

  return await makeOMDbRequest(params);
}

/**
 * Get detailed information by title and year
 */
export async function getOMDbDetailsByTitle(
  title: string,
  year?: string,
  type?: 'movie' | 'series',
  plot: 'short' | 'full' = 'short'
): Promise<OMDbDetailResponse> {
  const params: Record<string, string> = {
    t: title,
    plot: plot
  };

  if (year) {
    params.y = year;
  }

  if (type) {
    params.type = type;
  }

  return await makeOMDbRequest(params);
}

/**
 * Helper function to determine if poster is available
 */
export function hasValidPoster(poster: string): boolean {
  return poster && poster !== 'N/A' && poster.startsWith('http');
}

/**
 * Helper function to format OMDb data for display
 */
export function formatOMDbData(movie: OMDbMovie) {
  return {
    id: movie.imdbID,
    title: movie.Title,
    year: movie.Year && movie.Year !== 'N/A' ? movie.Year : '',
    type: movie.Type,
    plot: movie.Plot && movie.Plot !== 'N/A' ? movie.Plot : '',
    poster: hasValidPoster(movie.Poster) ? movie.Poster : '',
    rating: movie.imdbRating && movie.imdbRating !== 'N/A' ? movie.imdbRating : '',
    runtime: movie.Runtime && movie.Runtime !== 'N/A' ? movie.Runtime.replace(' min', '') : '',
    genres: movie.Genre && movie.Genre !== 'N/A' ? movie.Genre.split(', ') : [],
    director: movie.Director && movie.Director !== 'N/A' ? movie.Director : '',
    studio: movie.Writer && movie.Writer !== 'N/A' ? movie.Writer.split(',')[0] : '',
    languages: movie.Language && movie.Language !== 'N/A' ? movie.Language.split(', ') : [],
    totalSeasons: movie.Type === 'series' && movie.totalSeasons && movie.totalSeasons !== 'N/A' ?
      movie.totalSeasons : null,
    trailer: '', // OMDb doesn't provide trailer URLs
    tags: movie.Genre && movie.Genre !== 'N/A' ? movie.Genre.split(', ') : []
  };
}

/**
 * Search by IMDb ID specifically
 */
export async function searchOMDbByImdbId(imdbId: string): Promise<OMDbDetailResponse> {
  // Clean the IMDb ID (remove 'tt' prefix if present, then add it back)
  const cleanId = imdbId.replace(/^tt/, '');
  const formattedId = `tt${cleanId}`;

  return await getOMDbDetails(formattedId, 'full');
}

/**
 * Convert OMDb type to content type
 */
export function convertOMDbType(omdbType: string): 'movie' | 'tv' {
  return omdbType === 'series' ? 'tv' : 'movie';
}
