#!/usr/bin/env node

/**
 * Server Crash Diagnostic Script
 * This script helps identify why the Node.js server is crashing
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 StreamDB Server Crash Diagnostic');
console.log('=====================================\n');

// Check if we're in the right directory
const serverDir = path.join(__dirname, 'server');
if (!fs.existsSync(serverDir)) {
  console.error('❌ Error: server directory not found');
  console.error('   Please run this script from the StreamDB root directory');
  process.exit(1);
}

// Check critical files
const criticalFiles = [
  'server/index.js',
  'server/config/database.js',
  'server/routes/episodes.js',
  '.env'
];

console.log('📁 Checking critical files...');
for (const file of criticalFiles) {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file} - exists`);
  } else {
    console.log(`❌ ${file} - missing`);
  }
}

// Check syntax of critical JavaScript files
console.log('\n🔍 Checking syntax...');
const jsFiles = [
  'server/index.js',
  'server/config/database.js',
  'server/routes/episodes.js'
];

for (const file of jsFiles) {
  if (fs.existsSync(file)) {
    try {
      require(path.resolve(file));
      console.log(`✅ ${file} - syntax OK`);
    } catch (error) {
      console.log(`❌ ${file} - syntax error:`);
      console.log(`   ${error.message}`);
    }
  }
}

// Check environment variables
console.log('\n🔧 Checking environment variables...');
require('dotenv').config();

const requiredEnvVars = [
  'DB_USER',
  'DB_PASSWORD', 
  'DB_NAME',
  'JWT_SECRET',
  'PORT'
];

for (const envVar of requiredEnvVars) {
  if (process.env[envVar]) {
    console.log(`✅ ${envVar} - set`);
  } else {
    console.log(`❌ ${envVar} - missing`);
  }
}

// Test database connection
console.log('\n🗄️ Testing database connection...');
async function testDatabase() {
  try {
    const db = require('./server/config/database');
    await db.testConnection();
    console.log('✅ Database connection - OK');
  } catch (error) {
    console.log('❌ Database connection - failed:');
    console.log(`   ${error.message}`);
  }
}

// Check package.json dependencies
console.log('\n📦 Checking dependencies...');
try {
  const packageJson = JSON.parse(fs.readFileSync('server/package.json', 'utf8'));
  const dependencies = Object.keys(packageJson.dependencies || {});
  console.log(`✅ Found ${dependencies.length} dependencies in package.json`);
  
  // Check if node_modules exists
  if (fs.existsSync('server/node_modules')) {
    console.log('✅ node_modules directory exists');
  } else {
    console.log('❌ node_modules directory missing - run npm install');
  }
} catch (error) {
  console.log('❌ Error reading package.json:', error.message);
}

// Run database test
testDatabase().then(() => {
  console.log('\n🎯 Diagnostic complete!');
  console.log('\nIf all checks pass but server still crashes, check PM2 logs:');
  console.log('   pm2 logs streamdb-online --lines 50');
  console.log('   pm2 logs streamdb-online --err --lines 20');
}).catch((error) => {
  console.log('\n❌ Database test failed:', error.message);
  console.log('\nThis is likely the cause of the server crashes.');
  console.log('Check your database configuration and connection.');
});
